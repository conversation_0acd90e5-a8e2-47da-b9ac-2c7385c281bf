# frozen_string_literal: true

module Sentry
  module Utils
    module SampleRand
      def self.generate_from_trace_id(trace_id)
        seed = trace_id[0, 16].to_i(16)
        rng = Random.new(seed)

        value = rng.rand

        (value * 1_000_000).floor / 1_000_000.0
      end

      def self.generate_from_sampling_decision(sampled, sample_rate, trace_id = nil)
        if sample_rate.nil? || sample_rate <= 0.0 || sample_rate > 1.0
          return trace_id ? generate_from_trace_id(trace_id) : clamp_to_valid_range(Random.rand.round(6))
        end

        rng = trace_id ? Random.new(trace_id[0, 16].to_i(16)) : Random

        if sampled
          clamp_to_valid_range((rng.rand * sample_rate).round(6))
        else
          clamp_to_valid_range((sample_rate + rng.rand * (1.0 - sample_rate)).round(6))
        end
      end

      def self.valid?(sample_rand)
        return false unless sample_rand
        return false if sample_rand.is_a?(String) && sample_rand.empty?

        value = sample_rand.is_a?(String) ? sample_rand.to_f : sample_rand
        value >= 0.0 && value < 1.0
      end

      def self.format(sample_rand)
        truncated = (sample_rand * 1_000_000).floor / 1_000_000.0
        "%.6f" % truncated
      end

      private

      def self.clamp_to_valid_range(value)
        # Ensure the value is always in the range [0, 1) as required by the spec
        # If rounding results in 1.0, clamp it to the largest valid value
        return 0.999999 if value >= 1.0
        return 0.0 if value < 0.0
        value
      end
    end
  end
end
